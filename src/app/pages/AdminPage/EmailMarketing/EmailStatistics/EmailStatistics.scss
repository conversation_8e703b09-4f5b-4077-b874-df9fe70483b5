.email-statistics-container {
  .email-marketing-search-card {
    .form-filter {
      width: 100%;

      .search-form-item {
        margin-bottom: 16px;
      }

      .filter-form__date-picker {
        width: 100%;
        padding: 9px 11px !important;
      }

      .search-buttons-row {
        margin-top: 8px;
      }

      .search-buttons {
        display: flex;
        gap: 16px;
        justify-content: flex-end;
      }

      // Responsive styles
      @media (max-width: 768px) {
        .search-buttons {
          margin-top: 16px;
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }

  .email-marketing-info-card {
    .ant-statistic {
      text-align: center;
      
      .ant-statistic-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }
      
      .ant-statistic-content {
        font-size: 24px;
        font-weight: 600;
        color: #1890ff;
      }
    }
  }

  .email-marketing-table-card {
    margin-top: 16px;
    
    h3 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
    
    .ant-table {
      .ant-table-thead > tr > th {
        background-color: #fafafa;
        font-weight: 600;
        color: #262626;
      }
      
      .ant-table-tbody > tr:hover > td {
        background-color: #f5f5f5;
      }
    }
  }

  .ant-tabs {
    .ant-tabs-tab {
      font-weight: 500;
      
      &.ant-tabs-tab-active {
        font-weight: 600;
      }
    }
  }
}
