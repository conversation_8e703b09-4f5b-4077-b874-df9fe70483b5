import React, {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import {Link, useLocation, useNavigate} from "react-router-dom";
import {Card, Form, Input, Row, Col, Select, Tag, Tooltip, Modal} from "antd";
import {
  SearchOutlined,
  EditOutlined,
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  Bar<PERSON><PERSON>Outlined,
  MailOutlined,
  SendOutlined,
  CheckOutlined,
  SwapOutlined,
  DeleteOutlined,
  StopOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  ExclamationCircleOutlined
} from "@ant-design/icons";

import AntButton from "@component/AntButton";
import {AntForm} from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";

import {LINK} from "@link";
import {BUTTON, PAGINATION_INIT} from "@constant";

import {handlePagingData} from "@common/dataConverter";
import {
  handleSearchParams,
  orderColumn,
  paginationConfig,
  handleReplaceUrlSearch,
  formatTimeDate
} from "@common/functionCommons";

import {toast} from "@component/ToastProvider";

import "../EmailMarketing.scss";

import {getPaginationCampaigns, deleteCampaign, updateCampaignStatus, sendCampaignNow} from "@services/EmailMarketing";

// Định nghĩa các loại chiến dịch
const CAMPAIGN_TYPES = [
  {value: "automatic", label: t => t("CAMPAIGN_TYPE_AUTOMATED")},
  {value: "manual", label: t => t("CAMPAIGN_TYPE_MANUAL")}
];

// Định nghĩa các trạng thái chiến dịch
const CAMPAIGN_STATUS = [
  {value: "draft", label: t => t("CAMPAIGN_STATUS_DRAFT")},
  {value: "scheduled", label: t => t("CAMPAIGN_STATUS_SCHEDULED")},
  {value: "running", label: t => t("CAMPAIGN_STATUS_ACTIVE")},
  {value: "paused", label: t => t("CAMPAIGN_STATUS_PAUSED")},
  {value: "completed", label: t => t("CAMPAIGN_STATUS_COMPLETED")},
  {value: "failed", label: t => t("CAMPAIGN_STATUS_FAILED")}
];

const EmailCampaign = () => {
  const {t, i18n} = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [campaignData, setCampaignData] = useState(PAGINATION_INIT);
  const [isLoading, setLoading] = useState(false);

  const [formFilter] = Form.useForm();

  useEffect(() => {
    const {paging, query} = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getCampaignData(paging, query);
  }, [location.search]);

  const getCampaignData = async (paging = campaignData.paging, query = campaignData.query) => {
    setLoading(true);
    try {
      const dataResponse = await getPaginationCampaigns(paging, query);
      if (dataResponse) {
        setCampaignData(handlePagingData(dataResponse, query));
      }
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      toast.error(t("ERROR_FETCHING_CAMPAIGNS"));
    } finally {
      setLoading(false);
    }
  };

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, campaignData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, campaignData.paging.pageSize, {});
  };

  const handleDelete = (campaignId, campaignName) => {
    Modal.confirm({
      title: t("CONFIRM_DELETE_CAMPAIGN"),
      content: t("CONFIRM_DELETE_CAMPAIGN_CONTENT"),
      onOk: async () => {
        setLoading(true);
        try {
          const apiResponse = await deleteCampaign(campaignId);
          if (apiResponse) {
            toast.success(t("DELETE_CAMPAIGN_SUCCESS"));
            await getCampaignData();
          } else {
            toast.error(t("DELETE_CAMPAIGN_ERROR"));
          }
        } catch (error) {
          console.error("Error deleting campaign:", error);
          toast.error(t("DELETE_CAMPAIGN_ERROR"));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // Định nghĩa quy tắc chuyển đổi trạng thái theo API documentation
  const getValidTransitions = (currentStatus) => {
    const transitions = {
      draft: ["scheduled"],
      scheduled: ["running", "paused", "draft"],
      running: ["paused", "completed", "failed"],
      paused: ["running", "completed"],
      completed: [], // Không thể chuyển sang trạng thái nào khác
      failed: ["draft"]
    };
    return transitions[currentStatus] || [];
  };

  // Kiểm tra xem có thể chuyển đổi trạng thái hay không
  const canTransitionTo = (currentStatus, newStatus) => {
    const validTransitions = getValidTransitions(currentStatus);
    return validTransitions.includes(newStatus);
  };

  // Xử lý cập nhật trạng thái chiến dịch
  const handleStatusUpdate = async (id, currentStatus, newStatus, confirmMessage = null) => {
    // Kiểm tra tính hợp lệ của chuyển đổi trạng thái
    if (!canTransitionTo(currentStatus, newStatus)) {
      toast.error(t("INVALID_STATUS_TRANSITION", {from: currentStatus, to: newStatus}));
      return;
    }

    // Hiển thị xác nhận nếu có
    if (confirmMessage) {
      Modal.confirm({
        title: confirmMessage.title,
        content: confirmMessage.content,
        onOk: async () => {
          await performStatusUpdate(id, newStatus);
        },
      });
    } else {
      await performStatusUpdate(id, newStatus);
    }
  };

  // Thực hiện cập nhật trạng thái
  const performStatusUpdate = async (id, newStatus) => {
    setLoading(true);
    try {
      const response = await updateCampaignStatus(id, newStatus);
      if (response) {
        // Hiển thị thông báo thành công dựa trên trạng thái mới
        const successMessages = {
          running: "CAMPAIGN_ACTIVATED",
          paused: "CAMPAIGN_PAUSED",
          completed: "CAMPAIGN_COMPLETED",
          scheduled: "CAMPAIGN_SCHEDULED",
          draft: "CAMPAIGN_RESET_TO_DRAFT"
        };
        toast.success(t(successMessages[newStatus] || "UPDATE_CAMPAIGN_STATUS_SUCCESS"));
        await getCampaignData();
      } else {
        toast.error(t("UPDATE_CAMPAIGN_STATUS_ERROR"));
      }
    } catch (error) {
      console.error("Error updating campaign status:", error);
      // Xử lý lỗi cụ thể từ API
      if (error.response?.status === 400) {
        toast.error(error.response.data.message || t("STATUS_TRANSITION_ERROR"));
      } else {
        toast.error(t("UPDATE_CAMPAIGN_STATUS_ERROR"));
      }
    } finally {
      setLoading(false);
    }
  };

  // Các hàm xử lý cho từng loại chuyển đổi trạng thái
  const handleStartCampaign = (id, currentStatus) => {
    handleStatusUpdate(id, currentStatus, "running", {
      title: t("CONFIRM_START_CAMPAIGN"),
      content: t("START_CAMPAIGN_CONTENT")
    });
  };

  const handlePauseCampaign = (id, currentStatus) => {
    handleStatusUpdate(id, currentStatus, "paused", {
      title: t("CONFIRM_PAUSE_CAMPAIGN"),
      content: t("PAUSE_CAMPAIGN_CONTENT")
    });
  };

  const handleCompleteCampaign = (id, currentStatus) => {
    handleStatusUpdate(id, currentStatus, "completed", {
      title: t("CONFIRM_COMPLETE_CAMPAIGN"),
      content: t("COMPLETE_CAMPAIGN_CONTENT")
    });
  };

  const handleScheduleCampaign = (id, currentStatus) => {
    handleStatusUpdate(id, currentStatus, "scheduled", {
      title: t("CONFIRM_SCHEDULE_CAMPAIGN"),
      content: t("SCHEDULE_CAMPAIGN_CONTENT")
    });
  };

  const handleResetToDraft = (id, currentStatus) => {
    handleStatusUpdate(id, currentStatus, "draft", {
      title: t("CONFIRM_RESET_CAMPAIGN"),
      content: t("RESET_CAMPAIGN_CONTENT")
    });
  };

  // Xử lý gửi chiến dịch ngay lập tức (cho chiến dịch thủ công)
  const handleSendNow = async (id, campaignName) => {
    Modal.confirm({
      title: t("CONFIRM_SEND_NOW"),
      content: t("SEND_NOW_CONTENT"),
      onOk: async () => {
        setLoading(true);
        try {
          const response = await sendCampaignNow(id);
          if (response) {
            toast.success(t("CAMPAIGN_SENT_SUCCESS"));
            await getCampaignData(); // Refresh dữ liệu để cập nhật trạng thái
          } else {
            toast.error(t("CAMPAIGN_SEND_ERROR"));
          }
        } catch (error) {
          console.error("Error sending campaign:", error);
          toast.error(t("CAMPAIGN_SEND_ERROR"));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // Tạo các nút hành động dựa trên trạng thái hiện tại
  const getStatusActionButtons = (record) => {
    const {_id, status, type, name} = record;
    const buttons = [];

    // Nếu là chiến dịch thủ công, chỉ hiển thị nút Send Now
    if (type === "manual") {
      buttons.push(
        <Tooltip key="send-now" title={t("SEND_CAMPAIGN_NOW")}>
          <AntButton
            type="primary"
            size="small"
            icon={<SendOutlined/>}
            onClick={() => handleSendNow(_id, name)}
            className="action-button"
          />
        </Tooltip>
      );
      return buttons;
    }

    // Logic cho chiến dịch tự động (giữ nguyên logic cũ)
    const validTransitions = getValidTransitions(status);

    // Nút Start/Resume (chuyển sang running)
    if (validTransitions.includes("running")) {
      const isResume = status === "paused";
      buttons.push(
        <Tooltip key="start" title={t(isResume ? "RESUME_CAMPAIGN" : "START_CAMPAIGN")}>
          <AntButton
            type="primary"
            size="small"
            icon={<PlayCircleOutlined/>}
            onClick={() => handleStartCampaign(_id, status)}
            className="action-button"
          />
        </Tooltip>
      );
    }

    // Nút Pause (chuyển sang paused)
    if (validTransitions.includes("paused")) {
      buttons.push(
        <Tooltip key="pause" title={t("PAUSE_CAMPAIGN")}>
          <AntButton
            type="default"
            size="small"
            icon={<PauseCircleOutlined/>}
            onClick={() => handlePauseCampaign(_id, status)}
            className="action-button"
          />
        </Tooltip>
      );
    }

    // Nút Complete (chuyển sang completed)
    if (validTransitions.includes("completed")) {
      buttons.push(
        <Tooltip key="complete" title={t("COMPLETE_CAMPAIGN")}>
          <AntButton
            type="success"
            size="small"
            icon={<CheckOutlined/>}
            onClick={() => handleCompleteCampaign(_id, status)}
            className="action-button"
          />
        </Tooltip>
      );
    }

    // Nút Schedule (chuyển sang scheduled)
    if (validTransitions.includes("scheduled")) {
      buttons.push(
        <Tooltip key="schedule" title={t("SCHEDULE_CAMPAIGN")}>
          <AntButton
            type="warning"
            size="small"
            icon={<ClockCircleOutlined/>}
            onClick={() => handleScheduleCampaign(_id, status)}
            className="action-button"
          />
        </Tooltip>
      );
    }

    // Nút Reset to Draft (chuyển sang draft)
    if (validTransitions.includes("draft")) {
      buttons.push(
        <Tooltip key="reset" title={t("RESET_TO_DRAFT")}>
          <AntButton
            type="default"
            size="small"
            icon={<FileTextOutlined/>}
            onClick={() => handleResetToDraft(_id, status)}
            className="action-button"
          />
        </Tooltip>
      );
    }

    return buttons;
  };

  const columns = [
    {
      ...orderColumn(campaignData.paging),
      width: 80,
    },
    {
      title: t("CAMPAIGN_NAME"),
      dataIndex: "name",
      key: "name",
      width: 250,
      render: (text, record) => (
        <Link to={LINK.ADMIN.EMAIL_CAMPAIGN_ID.format(record._id)}>
          <span className="name-value">{text}</span>
        </Link>
      ),
    },
    {
      title: t("STATUS"),
      dataIndex: "status",
      key: "status",
      width: 150,
      render: (status) => {
        let color = "default";
        switch (status) {
          case "running":
            color = "success";
            break;
          case "paused":
            color = "warning";
            break;
          case "scheduled":
            color = "processing";
            break;
          case "completed":
            color = "cyan";
            break;
          case "failed":
            color = "error";
            break;
          default:
            color = "default";
        }

        return <Tag color={color}>{t(`CAMPAIGN_STATUS_${status.toUpperCase()}`)}</Tag>;
      },
    },
    {
      title: t("CAMPAIGN_TYPE"),
      dataIndex: "type",
      key: "type",
      width: 150,
      render: (type) => {
        const typeObj = CAMPAIGN_TYPES.find(item => item.value === type) || CAMPAIGN_TYPES[0];
        return typeObj.label(t);
      },
    },
    {
      title: t("TARGET_GROUPS"),
      dataIndex: "targetGroups",
      key: "targetGroups",
      width: 150,
      render: (targetGroups) => {
        if (!targetGroups || targetGroups.length === 0) return <span className="no-data">-</span>;

        // Hàm cắt ngắn tên nhóm
        const truncateGroupName = (name) => {
          if (name && name.length > 20) {
            return name.substring(0, 20) + '...';
          }
          return name;
        };

        // Tạo mảng màu sắc cho các tag
        const colors = ['blue', 'green', 'purple', 'cyan', 'magenta'];

        return (
          <div className="target-groups-container">
            {targetGroups.slice(0, 2).map((group, index) => (
              <Tooltip key={group._id} title={group.name}>
                <Tag
                  color={colors[index % colors.length]}
                  className="target-group-tag"
                >
                  {truncateGroupName(group.name)}
                </Tag>
              </Tooltip>
            ))}
            {targetGroups.length > 2 && (
              <Tooltip
                title={
                  <div className="tooltip-groups">
                    {targetGroups.slice(2).map((group, index) => (
                      <div key={group._id} className="tooltip-group-item">
                        {group.name}
                      </div>
                    ))}
                  </div>
                }
              >
                <Tag color="default" className="more-groups-tag">
                  +{targetGroups.length - 2}
                </Tag>
              </Tooltip>
            )}
          </div>
        );
      },
    },
    {
      title: t("STATISTICS"),
      key: "statistics",
      width: 350,
      render: (_, record) => (
        <div className="campaign-statistics">
          {record.statistics ? (
            <div className="stat-cards">
              <div className="stat-card sent">
                <div className="stat-icon">
                  <SendOutlined/>
                </div>
                <div className="stat-content">
                  <div className="stat-value">{record.statistics.totalSent || 0}</div>
                  <div className="stat-label">{t("EMAILS_SENT")}</div>
                </div>
              </div>

              <div className="stat-card open">
                <div className="stat-icon">
                  <MailOutlined/>
                </div>
                <div className="stat-content">
                  <div className="stat-value">{record.statistics.totalOpened || 0}</div>
                  <div className="stat-label">{t("EMAILS_OPENED")}</div>
                </div>
              </div>

              {/*<div className="stat-card click">*/}
              {/*  <div className="stat-icon">*/}
              {/*    <CheckOutlined />*/}
              {/*  </div>*/}
              {/*  <div className="stat-content">*/}
              {/*    <div className="stat-value">{record.statistics.clickRate || 0}%</div>*/}
              {/*    <div className="stat-label">{t("CLICK_RATE")}</div>*/}
              {/*  </div>*/}
              {/*</div>*/}

              {/*<div className="stat-card conversion">*/}
              {/*  <div className="stat-icon">*/}
              {/*    <SwapOutlined />*/}
              {/*  </div>*/}
              {/*  <div className="stat-content">*/}
              {/*    <div className="stat-value">{record.statistics.conversionRate || 0}%</div>*/}
              {/*    <div className="stat-label">{t("CONVERSION_RATE")}</div>*/}
              {/*  </div>*/}
              {/*</div>*/}
            </div>
          ) : (
            <span className="no-stats">{t("NO_STATISTICS_FOUND")}</span>
          )}
        </div>
      ),
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (text) => formatTimeDate(text),
    },
    {
      title: t("ACTIONS"),
      key: "actions",
      width: 250,
      align: "center",
      render: (_, record) => (
        <div className="email-marketing-actions">
          {/* Nút Edit */}
          <Tooltip title={t("EDIT")}>
            <Link to={LINK.ADMIN.EMAIL_CAMPAIGN_ID.format(record._id)}>
              <AntButton
                icon={<EditOutlined/>}
                className="btn-edit"
                size="small"
              />
            </Link>
          </Tooltip>

          {/* Các nút hành động trạng thái dựa trên trạng thái hiện tại */}
          {getStatusActionButtons(record)}

          {/* Nút View Statistics */}
          <Tooltip title={t("VIEW_STATISTICS")}>
            <Link to={`${LINK.ADMIN.EMAIL_STATISTICS}?campaignId=${record._id}`}>
              <AntButton
                type="primary"
                icon={<BarChartOutlined/>}
                className="btn-statistics"
                size="small"
              />
            </Link>
          </Tooltip>

          {/* Nút Delete - cho manual campaigns: có thể delete ở mọi trạng thái, cho automatic campaigns: chỉ draft và completed */}
          {(record.type === "manual" || record.status === "draft" || record.status === "completed") && (
            <Tooltip title={t("DELETE")}>
              <AntButton
                danger
                icon={<DeleteOutlined/>}
                onClick={() => handleDelete(record._id, record.name)}
                className="btn-delete"
                size="small"
              />
            </Tooltip>
          )}
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(campaignData.paging, campaignData.query, i18n.language);

  return (
    <Loading active={isLoading} transparent>
      <div className="email-marketing-container">
        <Card className="email-marketing-info-card">
          <div className="email-marketing-info-header">
            <div>
              <h1 className="email-marketing-title">{t("EMAIL_CAMPAIGN_MANAGEMENT")}</h1>
              <p className="email-marketing-description">{t("EMAIL_CAMPAIGN_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN.EMAIL_CAMPAIGN_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_CAMPAIGN")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="email-marketing-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter"
                   onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_CAMPAIGN_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined/>}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="status" className="search-form-item">
                      <Select
                        options={CAMPAIGN_STATUS}
                        allowClear
                        placeholder={t("FILTER_BY_STATUS")}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12} lg={8}>
                    <AntForm.Item name="type" className="search-form-item">
                      <Select
                        options={CAMPAIGN_TYPES}
                        placeholder={t("FILTER_BY_TYPE")}
                        allowClear
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="email-marketing-table-card">
          <TableAdmin
            columns={columns}
            dataSource={campaignData.rows}
            pagination={{...pagination}}
            scroll={{x: 1000}}
            className={"email-marketing-table"}
            rowClassName={() => "email-marketing-table-row"}
            locale={{emptyText: t("NO_CAMPAIGNS_FOUND")}}
            rowKey="_id"
          />
        </Card>
      </div>
    </Loading>
  );
};

export default EmailCampaign;
